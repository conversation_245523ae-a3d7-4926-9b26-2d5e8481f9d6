#!/bin/bash

# FCC Pump Readings API - curl 命令示例
# 用于手动修改 pump-readings 数据

# =============================================================================
# 配置信息 (从 configs/config.yaml 获取)
# =============================================================================
BASE_URL="http://192.168.8.114:8080"
API_KEY="fcc-secret-api-key"

# =============================================================================
# 基础 curl 命令模板
# =============================================================================

# 1. 更新泵码读数 (仅 end_totalizer)
update_pump_readings_volume_only() {
    local external_transaction_id="$1"
    local end_totalizer="$2"
    
    echo "更新交易 ${external_transaction_id} 的体积读数..."
    
    curl -X PATCH \
        "${BASE_URL}/api/v1/fuel-transactions/${external_transaction_id}/pump-readings" \
        -H "Content-Type: application/json" \
        -H "X-API-Key: ${API_KEY}" \
        -H "User-Agent: FCC-Service-Transaction-Sync/1.0" \
        -d "{
            \"end_totalizer\": ${end_totalizer}
        }" \
        -v
}

# 2. 更新泵码读数 (包含 end_totalizer 和 end_amount_reading)
update_pump_readings_full() {
    local external_transaction_id="$1"
    local end_totalizer="$2"
    local end_amount_reading="$3"
    
    echo "更新交易 ${external_transaction_id} 的完整泵码读数..."
    
    curl -X PATCH \
        "${BASE_URL}/api/v1/fuel-transactions/${external_transaction_id}/pump-readings" \
        -H "Content-Type: application/json" \
        -H "X-API-Key: ${API_KEY}" \
        -H "User-Agent: FCC-Service-Transaction-Sync/1.0" \
        -d "{
            \"end_totalizer\": ${end_totalizer},
            \"end_amount_reading\": ${end_amount_reading}
        }" \
        -v
}

# 3. 直接执行的 curl 命令示例
echo "=============================================================================
FCC Pump Readings API - 手动修改示例
=============================================================================

请替换以下参数：
- EXTERNAL_TRANSACTION_ID: 外部交易ID (从数据库 transactions.external_transaction_id 字段获取)
- END_TOTALIZER: 结束体积读数 (例如: 1050.500)
- END_AMOUNT_READING: 结束金额读数 (例如: 5378.75)

示例 1: 仅更新体积读数
"

cat << 'EOF'
curl -X PATCH \
    "http://192.168.8.114:8080/api/v1/fuel-transactions/YOUR_EXTERNAL_TRANSACTION_ID/pump-readings" \
    -H "Content-Type: application/json" \
    -H "X-API-Key: fcc-secret-api-key" \
    -H "User-Agent: FCC-Service-Transaction-Sync/1.0" \
    -d '{
        "end_totalizer": 1050.500
    }' \
    -v
EOF

echo "

示例 2: 更新完整泵码读数
"

cat << 'EOF'
curl -X PATCH \
    "http://192.168.8.114:8080/api/v1/fuel-transactions/YOUR_EXTERNAL_TRANSACTION_ID/pump-readings" \
    -H "Content-Type: application/json" \
    -H "X-API-Key: fcc-secret-api-key" \
    -H "User-Agent: FCC-Service-Transaction-Sync/1.0" \
    -d '{
        "end_totalizer": 1050.500,
        "end_amount_reading": 5378.75
    }' \
    -v
EOF

echo "

=============================================================================
如何获取 external_transaction_id
=============================================================================

1. 连接到数据库:
   psql -h localhost -p 5432 -U postgres -d fcc

2. 查询交易的外部ID:
   SELECT id, external_transaction_id, device_id, nozzle_id, 
          actual_volume, actual_amount, sync_status
   FROM transactions 
   WHERE external_transaction_id IS NOT NULL 
   ORDER BY created_at DESC 
   LIMIT 10;

3. 或者查询特定交易:
   SELECT id, external_transaction_id, sync_status
   FROM transactions 
   WHERE id = 'YOUR_FCC_TRANSACTION_ID';

=============================================================================
使用函数调用示例
=============================================================================

# 使用函数更新 (需要先 source 这个脚本)
# source pump-readings-curl-examples.sh

# 示例调用:
# update_pump_readings_volume_only \"ext-tx-12345\" 1050.500
# update_pump_readings_full \"ext-tx-12345\" 1050.500 5378.75

=============================================================================
响应示例
=============================================================================

成功响应 (HTTP 200):
{
  \"message\": \"Pump readings updated successfully\",
  \"transaction_id\": \"ext-tx-12345\",
  \"updated_fields\": [\"end_totalizer\", \"end_amount_reading\"]
}

错误响应示例:
- HTTP 404: 交易不存在
- HTTP 400: 数据格式错误
- HTTP 401: API Key 无效
- HTTP 500: 服务器内部错误

=============================================================================
"

# 如果脚本被直接执行，显示帮助信息
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    echo "脚本已加载，可以使用以下函数:"
    echo "- update_pump_readings_volume_only <external_transaction_id> <end_totalizer>"
    echo "- update_pump_readings_full <external_transaction_id> <end_totalizer> <end_amount_reading>"
    echo ""
    echo "或者直接复制上面的 curl 命令示例进行使用。"
fi
