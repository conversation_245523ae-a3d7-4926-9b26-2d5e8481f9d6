# FCC Pump Readings API - 快速 curl 参考

## 基础信息
- **Base URL**: `http://192.168.8.114:8080`
- **API Key**: `fcc-secret-api-key`
- **端点**: `/api/v1/fuel-transactions/{external_transaction_id}/pump-readings`
- **方法**: `PATCH`

## 快速 curl 命令

### 1. 仅更新体积读数 (end_totalizer)

```bash
curl -X PATCH \
    "http://192.168.8.114:8080/api/v1/fuel-transactions/YOUR_EXTERNAL_TRANSACTION_ID/pump-readings" \
    -H "Content-Type: application/json" \
    -H "X-API-Key: fcc-secret-api-key" \
    -d '{"end_totalizer": 1050.500}' \
    -v
```

### 2. 更新完整泵码读数 (体积 + 金额)

```bash
curl -X PATCH \
    "http://192.168.8.114:8080/api/v1/fuel-transactions/YOUR_EXTERNAL_TRANSACTION_ID/pump-readings" \
    -H "Content-Type: application/json" \
    -H "X-API-Key: fcc-secret-api-key" \
    -d '{
        "end_totalizer": 1050.500,
        "end_amount_reading": 5378.75
    }' \
    -v
```

## 获取 external_transaction_id

```sql
-- 查看最近的交易及其外部ID
SELECT id, external_transaction_id, device_id, nozzle_id, 
       actual_volume, actual_amount, sync_status
FROM transactions 
WHERE external_transaction_id IS NOT NULL 
ORDER BY created_at DESC 
LIMIT 10;

-- 查询特定交易的外部ID
SELECT id, external_transaction_id, sync_status
FROM transactions 
WHERE id = 'YOUR_FCC_TRANSACTION_ID';
```

## 数据格式说明

根据代码分析，pump-readings 更新支持以下字段：

- `end_totalizer` (必需): 结束体积读数，对应 `tx.EndPumpVolumeReading`
- `end_amount_reading` (可选): 结束金额读数，对应 `tx.EndPumpAmountReading`

## 响应示例

**成功 (HTTP 200)**:
```json
{
  "message": "Pump readings updated successfully",
  "transaction_id": "ext-tx-12345"
}
```

**常见错误**:
- `HTTP 404`: 交易不存在
- `HTTP 400`: 数据格式错误  
- `HTTP 401`: API Key 无效

## 实际使用步骤

1. **查询外部交易ID**:
   ```sql
   SELECT external_transaction_id FROM transactions WHERE id = 'your-fcc-tx-id';
   ```

2. **替换 curl 命令中的参数**:
   - `YOUR_EXTERNAL_TRANSACTION_ID` → 实际的外部交易ID
   - `1050.500` → 实际的结束体积读数
   - `5378.75` → 实际的结束金额读数

3. **执行 curl 命令**

4. **检查响应状态码和内容**
